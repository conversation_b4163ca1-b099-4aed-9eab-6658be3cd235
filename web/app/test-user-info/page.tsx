'use client'
import { FC } from 'react'
import UserInfoDisplay from '@/components/iam/user-info-display'
import useUrlUserInfo from '@/hooks/use-url-user-info'
import useUnifiedUserInfo from '@/hooks/use-unified-user-info'
import useIAMUserInfo from '@/hooks/use-iam-user-info'

/**
 * 用户信息测试页面 - 用于验证不同hooks的工作情况
 */
const TestUserInfoPage: FC = () => {
  // 测试原有的useUrlUserInfo hook（现在内部使用统一逻辑）
  const urlUserInfo = useUrlUserInfo({
    onUserInfoChange: (userInfo) => {
      console.log('useUrlUserInfo 用户信息变化:', userInfo)
    }
  })

  // 测试统一用户信息hook
  const unifiedUserInfo = useUnifiedUserInfo({
    onUserInfoChange: (userInfo) => {
      console.log('useUnifiedUserInfo 用户信息变化:', userInfo)
    },
    preferIAM: true
  })

  // 测试IAM专用hook
  const iamUserInfo = useIAMUserInfo({
    onUserInfoChange: (userInfo) => {
      console.log('useIAMUserInfo 用户信息变化:', userInfo)
    },
    onLoginStatusChange: (isLoggedIn) => {
      console.log('IAM登录状态变化:', isLoggedIn)
    }
  })

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">用户信息Hook测试页面</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* 原有useUrlUserInfo Hook测试 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">useUrlUserInfo Hook (兼容性测试)</h2>
            <div className="space-y-2">
              <div><strong>数据状态:</strong> {Object.keys(urlUserInfo).length > 0 ? '有数据' : '无数据'}</div>
              {urlUserInfo.realName && <div><strong>真实姓名:</strong> {urlUserInfo.realName}</div>}
              {urlUserInfo.userName && <div><strong>用户名:</strong> {urlUserInfo.userName}</div>}
              {urlUserInfo.phone && <div><strong>手机号:</strong> {urlUserInfo.phone}</div>}
              {urlUserInfo.employeeNum && <div><strong>员工号:</strong> {urlUserInfo.employeeNum}</div>}
              <details className="mt-4">
                <summary className="cursor-pointer font-medium">完整数据</summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
                  {JSON.stringify(urlUserInfo, null, 2)}
                </pre>
              </details>
            </div>
          </div>

          {/* 统一用户信息Hook测试 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">useUnifiedUserInfo Hook</h2>
            <div className="space-y-2">
              <div><strong>加载状态:</strong> {unifiedUserInfo.isLoading ? '加载中' : '已加载'}</div>
              <div><strong>数据状态:</strong> {unifiedUserInfo.hasUserInfo ? '有数据' : '无数据'}</div>
              <div><strong>数据来源:</strong> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${
                  unifiedUserInfo.isFromIAM ? 'bg-green-100 text-green-800' :
                  unifiedUserInfo.isFromURL ? 'bg-blue-100 text-blue-800' :
                  unifiedUserInfo.isFromCache ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {unifiedUserInfo.isFromIAM ? 'IAM认证' :
                   unifiedUserInfo.isFromURL ? 'URL参数' :
                   unifiedUserInfo.isFromCache ? '本地缓存' : '无'}
                </span>
              </div>
              {unifiedUserInfo.userInfo.realName && <div><strong>真实姓名:</strong> {unifiedUserInfo.userInfo.realName}</div>}
              {unifiedUserInfo.userInfo.phone && <div><strong>手机号:</strong> {unifiedUserInfo.userInfo.phone}</div>}
            </div>
          </div>

          {/* IAM专用Hook测试 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">useIAMUserInfo Hook</h2>
            <div className="space-y-2">
              <div><strong>加载状态:</strong> {iamUserInfo.isLoading ? '加载中' : '已加载'}</div>
              <div><strong>登录状态:</strong> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${
                  iamUserInfo.isLoggedIn ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {iamUserInfo.isLoggedIn ? '已登录' : '未登录'}
                </span>
              </div>
              {iamUserInfo.realName && <div><strong>真实姓名:</strong> {iamUserInfo.realName}</div>}
              {iamUserInfo.userId && <div><strong>用户ID:</strong> {iamUserInfo.userId}</div>}
              {iamUserInfo.isLoggedIn && (
                <button 
                  onClick={() => iamUserInfo.logout()}
                  className="mt-2 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                >
                  登出
                </button>
              )}
            </div>
          </div>

          {/* 操作区域 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">操作区域</h2>
            <div className="space-y-4">
              <button 
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                刷新页面
              </button>
              <button 
                onClick={() => {
                  localStorage.clear()
                  window.location.reload()
                }}
                className="w-full px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
              >
                清除缓存并刷新
              </button>
              <button 
                onClick={() => {
                  window.location.href = '/ai_dify/loginIAM?redirectUrl=' + encodeURIComponent(window.location.href)
                }}
                className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                跳转到IAM登录
              </button>
            </div>
          </div>
        </div>

        {/* 使用组件展示 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <UserInfoDisplay showUnified={true} />
          <UserInfoDisplay showIAMOnly={true} />
        </div>

        {/* 使用说明 */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">测试说明</h2>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• <strong>useUrlUserInfo</strong>: 保持原有API兼容性，内部使用新的统一逻辑</p>
            <p>• <strong>useUnifiedUserInfo</strong>: 新的统一用户信息hook，支持多种数据源</p>
            <p>• <strong>useIAMUserInfo</strong>: 专门处理IAM认证的hook</p>
            <p>• 数据获取优先级: IAM Cookie → URL参数 → localStorage缓存</p>
            <p>• 可以通过URL参数测试: <code>?employeeNum=123&phone=13800138000</code></p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TestUserInfoPage
