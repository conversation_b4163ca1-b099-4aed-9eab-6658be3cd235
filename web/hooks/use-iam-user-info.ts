'use client'
import { useState, useEffect } from 'react'
import { getCurrentUser, logout, type IAMUserInfo } from '@/utils/iam-auth'

interface UseIAMUserInfoProps {
  onUserInfoChange?: (userInfo: IAMUserInfo | null) => void
  onLoginStatusChange?: (isLoggedIn: boolean) => void
}

interface IAMUserInfoState {
  isLoggedIn: boolean
  userInfo: IAMUserInfo | null
  userId: string | null
  realName: string | null
  isLoading: boolean
}

const useIAMUserInfo = (props: UseIAMUserInfoProps = {}) => {
  const { onUserInfoChange, onLoginStatusChange } = props

  const [state, setState] = useState<IAMUserInfoState>({
    isLoggedIn: false,
    userInfo: null,
    userId: null,
    realName: null,
    isLoading: true
  })

  // 刷新用户信息
  const refreshUserInfo = () => {
    const { isLoggedIn, userInfo, userId, realName } = getCurrentUser()
    
    setState({
      isLoggedIn,
      userInfo,
      userId,
      realName,
      isLoading: false
    })

    // 触发回调
    onUserInfoChange?.(userInfo)
    onLoginStatusChange?.(isLoggedIn)
  }

  // 登出函数
  const handleLogout = (returnUrl?: string) => {
    logout(returnUrl)
    // 登出后立即更新状态
    setState({
      isLoggedIn: false,
      userInfo: null,
      userId: null,
      realName: null,
      isLoading: false
    })
    onUserInfoChange?.(null)
    onLoginStatusChange?.(false)
  }

  useEffect(() => {
    refreshUserInfo()
  }, [])

  return {
    ...state,
    refreshUserInfo,
    logout: handleLogout
  }
}

export default useIAMUserInfo
