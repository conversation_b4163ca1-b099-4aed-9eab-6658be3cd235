'use client'
import useUnifiedUserInfo from './use-unified-user-info'

// 导出getQueryParams以保持兼容性
export { getQueryParams } from './use-unified-user-info'

interface UserInfo {
  employeeNum?: string
  phone?: string
  browser_type?: string
  [key: string]: any
}

/**
 * 兼容性Hook - 使用新的统一用户信息逻辑
 * 优先从IAM Cookie获取用户信息，然后是URL参数，最后是localStorage缓存
 */
const useUrlUserInfo = (props: {
  onUserInfoChange?: (userInfo: UserInfo) => void
} = {}) => {
  const { onUserInfoChange } = props || {}

  const { userInfo } = useUnifiedUserInfo({
    onUserInfoChange,
    preferIAM: true // 优先使用IAM认证
  })

  return userInfo
}

export default useUrlUserInfo 