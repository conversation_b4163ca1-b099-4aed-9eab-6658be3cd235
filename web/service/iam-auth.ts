/**
 * IAM认证相关API接口
 */

import { get } from './base'
import type { IAMUserInfo } from '@/utils/iam-auth'

// IAM认证响应类型定义
export interface IAMResponse {
  code?: number
  status?: string
  success?: boolean
  data?: IAMUserInfo
  user?: IAMUserInfo
  userInfo?: IAMUserInfo
  message?: string
  [key: string]: any
}

export interface ProcessedIAMResponse {
  success: boolean
  userInfo: IAMUserInfo | null
  userId: string | null
  message?: string
  originalResponse?: IAMResponse
}

/**
 * 根据ticket获取用户信息
 * @param ticket IAM返回的ticket
 * @param callback 回调地址
 * @returns 处理后的用户信息
 */
export const getUserInfo = async (ticket: string, callback: string): Promise<ProcessedIAMResponse> => {
  try {
    console.log('调用IAM接口:', { ticket, callback })

    const response = await get<any>('/ai_dify/getUserInfo', {
      params: {
        ticket,
        callback
      }
    })

    console.log('IAM接口响应:', response)

    // 检查响应是否包含必要的用户信息字段
    if (response) {
      // 构建标准的用户信息对象
      const userInfo: IAMUserInfo = {
        staffCode: response.staffCode || '',
        employeeStatus: response.employeeStatus || '',
        pkGroup: response.pkGroup || '',
        pkDept: response.pkDept || '',
        sort: response.sort || '',
        userName: response.userName || '',
        realName: response.realName || '',
        pkOrg: response.pkOrg || '',
        phone: response.phone || '',
        psnCode: response.psnCode || '',
        groupUserName: response.groupUserName || '',
        ZrUuid: response.ZrUuid || '',
        employeeNum: response.employeeNum || ''
      }

      return {
        success: true,
        userInfo,
        userId: userInfo.userName,
        originalResponse: response
      }
    } else {
      return {
        success: false,
        userInfo: null,
        userId: null,
        message: '响应中未找到有效的用户信息',
        originalResponse: response
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)

    let message = '网络请求失败'
    if (error instanceof Error) {
      message = error.message
    } else if (typeof error === 'string') {
      message = error
    }

    return {
      success: false,
      userInfo: null,
      userId: null,
      message,
      originalResponse: undefined
    }
  }
}

/**
 * 验证用户信息有效性（可选接口）
 * @param userInfo 用户信息对象
 * @returns 验证结果
 */
export const validateUserInfo = async (userInfo: IAMUserInfo): Promise<{ valid: boolean; message?: string }> => {
  try {
    // 如果后端提供了验证接口，可以在这里调用
    // const response = await get<any>('/ai_dify/validateUserInfo', {
    //   params: { userName: userInfo.userName }
    // })

    // 基本字段验证
    if (!userInfo.userName || userInfo.userName.trim().length === 0) {
      return { valid: false, message: '用户名不能为空' }
    }

    if (!userInfo.realName || userInfo.realName.trim().length === 0) {
      return { valid: false, message: '真实姓名不能为空' }
    }

    if (!userInfo.staffCode || userInfo.staffCode.trim().length === 0) {
      return { valid: false, message: '员工编码不能为空' }
    }

    return { valid: true }
  } catch (error) {
    console.error('验证用户信息失败:', error)
    return { valid: false, message: '验证失败' }
  }
}

/**
 * 处理IAM认证错误
 * @param error 错误信息
 * @returns 用户友好的错误消息
 */
export const handleIAMError = (error: any): string => {
  if (typeof error === 'string') {
    return error
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  if (error?.response?.data?.message) {
    return error.response.data.message
  }
  
  if (error?.message) {
    return error.message
  }
  
  return '认证过程中发生未知错误'
}

/**
 * 检查ticket格式是否有效
 * @param ticket ticket字符串
 * @returns 是否有效
 */
export const isValidTicket = (ticket: string): boolean => {
  if (!ticket || typeof ticket !== 'string') {
    return false
  }
  
  // IAM的ticket通常以ST-开头
  return ticket.startsWith('ST-') && ticket.length > 10
}

/**
 * 构建IAM登出URL
 * @param returnUrl 登出后返回的URL
 * @returns IAM登出URL
 */
export const buildIAMLogoutUrl = (returnUrl?: string): string => {
  const iamLogoutUrl = 'https://iam.cec.com.cn/cas/logout'
  
  if (returnUrl) {
    return `${iamLogoutUrl}?service=${encodeURIComponent(returnUrl)}`
  }
  
  return iamLogoutUrl
}
