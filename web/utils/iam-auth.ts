/**
 * IAM认证相关工具函数
 */

// IAM用户信息接口定义
export interface IAMUserInfo {
  staffCode: string
  employeeStatus: string
  pkGroup: string
  pkDept: string
  sort: string
  userName: string
  realName: string
  pkOrg: string
  phone: string
  psnCode: string
  groupUserName: string
  ZrUuid: string
  employeeNum: string
}

// IAM配置常量
export const IAM_CONFIG = {
  iamUrl: 'https://iam.cec.com.cn/cas/login',
  cookieName: 'dify_iam_user_info',
  defaultRedirectPath: IAM_CONFIG.defaultRedirectPath,
  maxRetries: 3,
  cookieExpires: 8 // 小时
} as const

// Cookie管理 - 保存完整的用户信息
export const setUserInfoCookie = (userInfo: IAMUserInfo, expires = 8) => {
  const date = new Date()
  date.setTime(date.getTime() + (expires * 60 * 60 * 1000))
  const expiresStr = `expires=${date.toUTCString()}`
  // 根据项目实际域名调整
  const domain = location.hostname
  const userInfoStr = JSON.stringify(userInfo)
  // 使用encodeURIComponent确保特殊字符正确编码
  document.cookie = `${IAM_CONFIG.cookieName}=${encodeURIComponent(userInfoStr)}; ${expiresStr}; path=/; SameSite=Lax; domain=${domain}`
}

export const getUserInfoCookie = (): IAMUserInfo | null => {
  if (typeof document === 'undefined') return null

  const cookies = document.cookie.split(';')
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === IAM_CONFIG.cookieName) {
      try {
        const decodedValue = decodeURIComponent(value)
        return JSON.parse(decodedValue) as IAMUserInfo
      } catch (error) {
        console.error('解析用户信息Cookie失败:', error)
        return null
      }
    }
  }
  return null
}

export const clearUserInfoCookie = () => {
  if (typeof document === 'undefined') return

  const domain = location.hostname
  document.cookie = `${IAM_CONFIG.cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=Lax; domain=${domain}`
}

// 兼容性函数 - 获取用户ID
export const getUserIdCookie = (): string | null => {
  const userInfo = getUserInfoCookie()
  return userInfo?.userName || null
}

// 认证状态检查
export const checkAuthStatus = () => {
  const userInfo = getUserInfoCookie()
  const userId = userInfo?.userName || null
  const query = getQueryParams()
  return {
    hasUserId: !!userId,
    hasUserInfo: !!userInfo,
    hasTicket: !!query.ticket,
    userId: userId,
    userInfo: userInfo,
    ticket: query.ticket
  }
}

// 获取URL查询参数（适配Next.js）
export const getQueryParams = (): Record<string, string> => {
  if (typeof window === 'undefined') return {}
  
  const params: Record<string, string> = {}
  const searchParams = new URLSearchParams(window.location.search)
  
  for (const [key, value] of searchParams.entries()) {
    params[key] = value
  }
  
  return params
}

// 解析URL参数
export const parseUrlParams = (defaultRedirectPath = IAM_CONFIG.defaultRedirectPath) => {
  const query = getQueryParams()
  let redirectUrl = defaultRedirectPath
  let otherParams: Record<string, string> = {}
  
  if (query.params) {
    try {
      const decodedParams = atob(query.params)
      const paramsObj = new URLSearchParams(decodedParams)
      redirectUrl = paramsObj.get('redirectUrl') || defaultRedirectPath
      otherParams = Object.fromEntries(paramsObj.entries())
      delete otherParams.redirectUrl
    } catch (error) {
      console.error('参数解密失败:', error)
      redirectUrl = query.redirectUrl || defaultRedirectPath
      const { ticket, redirectUrl: _, params, ...fallbackParams } = query
      otherParams = fallbackParams
    }
  } else {
    redirectUrl = query.redirectUrl || defaultRedirectPath
    const { ticket, redirectUrl: _, ...fallbackParams } = query
    otherParams = fallbackParams
  }
  
  return { redirectUrl, otherParams }
}

// 构建callback URL
export const buildCallbackUrl = (redirectUrl: string, otherParams: Record<string, string> = {}) => {
  if (typeof window === 'undefined') return ''
  
  const currentUrl = `${window.location.origin}/ai_dify/loginIAM`
  const serviceParams = new URLSearchParams({
    redirectUrl: redirectUrl,
    ...otherParams
  })
  
  // base64编码参数，避免IAM系统误识别
  const encodedParams = btoa(serviceParams.toString())
  return `${currentUrl}?params=${encodeURIComponent(encodedParams)}`
}

// 跳转到目标页面
export const redirectToTarget = (redirectUrl = IAM_CONFIG.defaultRedirectPath, otherParams: Record<string, string> = {}) => {
  if (typeof window === 'undefined') return
  
  let routePath = redirectUrl
  
  // 处理完整URL
  if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
    try {
      const url = new URL(redirectUrl)
      routePath = url.pathname + url.search + url.hash
    } catch (error) {
      console.error('URL解析失败:', error)
      routePath = IAM_CONFIG.defaultRedirectPath
    }
  }
  
  if (!routePath.startsWith('/')) {
    routePath = '/' + routePath
  }
  
  // 构建最终的跳转URL
  const targetUrl = new URL(routePath, window.location.origin)
  Object.entries(otherParams).forEach(([key, value]) => {
    targetUrl.searchParams.set(key, value)
  })
  
  window.location.href = targetUrl.toString()
}

// 重定向到IAM
export const redirectToIAM = (redirectUrl: string, otherParams: Record<string, string> = {}) => {
  if (typeof window === 'undefined') return

  const callbackUrl = buildCallbackUrl(redirectUrl, otherParams)
  const iamUrl = 'https://iam.cec.com.cn/cas/login'
  const fullIamUrl = `${iamUrl}?service=${encodeURIComponent(callbackUrl)}`

  console.log('重定向到IAM:', fullIamUrl)
  window.location.href = fullIamUrl
}

// 获取当前登录用户信息的便捷函数
export const getCurrentUser = (): {
  isLoggedIn: boolean
  userInfo: IAMUserInfo | null
  userId: string | null
  realName: string | null
} => {
  const userInfo = getUserInfoCookie()
  return {
    isLoggedIn: !!userInfo,
    userInfo,
    userId: userInfo?.userName || null,
    realName: userInfo?.realName || null
  }
}

// 登出函数
export const logout = (returnUrl?: string) => {
  // 清除本地Cookie
  clearUserInfoCookie()

  // 如果需要，可以重定向到IAM登出页面
  if (returnUrl) {
    const iamLogoutUrl = 'https://iam.cec.com.cn/cas/logout'
    const fullLogoutUrl = `${iamLogoutUrl}?service=${encodeURIComponent(returnUrl)}`
    window.location.href = fullLogoutUrl
  } else {
    // 刷新页面或重定向到登录页
    window.location.reload()
  }
}

