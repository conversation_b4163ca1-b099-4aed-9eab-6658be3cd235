# IAM单点登录实现说明

## 概述

本实现基于IAM_SSO_INTEGRATION_GUIDE.md中的React方案，结合项目的Next.js架构和代码规范，实现了完整的IAM单点登录功能。

## 文件结构

```
web/
├── app/
│   └── ai_dify/
│       └── loginIAM/
│           └── page.tsx                 # IAM认证页面组件
├── service/
│   └── iam-auth.ts                      # IAM认证API接口封装
├── utils/
│   └── iam-auth.ts                      # IAM认证工具函数
└── IAM_IMPLEMENTATION_README.md         # 本说明文档
```

## 核心功能

### 1. IAM认证工具函数 (`web/utils/iam-auth.ts`)

**用户信息类型定义**：
- `IAMUserInfo` - 完整的用户信息接口，包含所有后端返回的字段

**Cookie管理**：
- `setUserInfoCookie(userInfo, expires)` - 设置完整的用户信息Cookie
- `getUserInfoCookie()` - 获取完整的用户信息Cookie
- `clearUserInfoCookie()` - 清除用户信息Cookie
- `getUserIdCookie()` - 兼容性函数，获取用户ID

**参数处理**：
- `getQueryParams()` - 获取URL查询参数
- `parseUrlParams(defaultPath)` - 解析编码的URL参数
- `buildCallbackUrl(redirectUrl, params)` - 构建IAM回调URL

**页面跳转**：
- `redirectToTarget(url, params)` - 跳转到目标页面
- `redirectToIAM(redirectUrl, params)` - 重定向到IAM认证

**便捷函数**：
- `getCurrentUser()` - 获取当前登录用户信息
- `logout(returnUrl)` - 登出并可选择重定向到IAM登出页面

### 2. IAM认证API接口 (`web/service/iam-auth.ts`)

**主要接口**：
- `getUserInfo(ticket, callback)` - 调用后端接口获取完整用户信息
- `validateUserInfo(userInfo)` - 验证用户信息有效性（可选）
- `handleIAMError(error)` - 统一错误处理
- `isValidTicket(ticket)` - 验证ticket格式

**类型定义**：
- `IAMResponse` - API响应接口
- `ProcessedIAMResponse` - 处理后的响应接口（包含完整用户信息）

### 3. IAM认证页面 (`web/app/ai_dify/loginIAM/page.tsx`)

**核心功能**：
- 自动检查用户登录状态
- 处理IAM认证回调
- 错误处理和重试机制
- 用户友好的界面提示

## 认证流程

1. **用户访问** `/ai_dify/loginIAM`
2. **检查缓存** - 如果已有用户信息Cookie，直接跳转
3. **处理回调** - 如果URL中有ticket参数，调用API验证
4. **IAM重定向** - 如果无ticket，重定向到IAM认证页面
5. **获取用户信息** - ticket验证成功后获取完整用户信息
6. **设置Cookie** - 缓存完整用户信息到Cookie
7. **跳转目标** - 根据参数跳转到指定页面

## 配置说明

### 环境配置

在 `web/utils/iam-auth.ts` 中的 `IAM_CONFIG` 对象：

```typescript
export const IAM_CONFIG = {
  iamUrl: 'https://iam.cec.com.cn/cas/login',    // IAM认证地址
  cookieName: 'dify_iam_user_id',                // Cookie名称
  defaultRedirectPath: '/apps',                   // 默认跳转页面
  maxRetries: 3,                                 // 最大重试次数
  cookieExpires: 8                               // Cookie过期时间(小时)
}
```

### Cookie域名配置

在生产环境中，需要在 `setUserIdCookie` 和 `clearUserIdCookie` 函数中设置正确的域名：

```typescript
const domain = process.env.NODE_ENV === 'production' ? '; domain=.your-domain.com' : ''
```

## 后端接口要求

后端需要提供以下接口：

```
GET /ai_dify/getUserInfo?ticket=ST-xxx&callback=https://domain.com/ai_dify/loginIAM?params=xxx
```

**响应格式**（直接返回用户信息JSON对象）：
```json
{
  "staffCode": "E000082439",
  "employeeStatus": "2",
  "pkGroup": "CEC",
  "pkDept": "D9000000160",
  "sort": "0",
  "userName": "P0000220327",
  "realName": "陈果",
  "pkOrg": "001-01",
  "phone": "13638693360",
  "psnCode": "P900000453",
  "groupUserName": "chengguo001",
  "ZrUuid": "1dd6d1b6f4624e4ce830408415681577",
  "employeeNum": "chenguo3360"
}
```

## 使用方法

### 1. 直接访问认证页面

```
https://your-domain.com/ai_dify/loginIAM
```

### 2. 带参数访问（指定跳转目标）

```
https://your-domain.com/ai_dify/loginIAM?redirectUrl=/specific-page&param1=value1
```

### 3. 编程方式跳转

```typescript
import { redirectToIAM, getCurrentUser, logout } from '@/utils/iam-auth'

// 跳转到IAM认证，认证成功后返回指定页面
redirectToIAM('/target-page', { param1: 'value1' })

// 获取当前登录用户信息
const { isLoggedIn, userInfo, userId, realName } = getCurrentUser()
if (isLoggedIn) {
  console.log(`欢迎，${realName}！用户名：${userId}`)
  console.log('完整用户信息：', userInfo)
}

// 登出
logout() // 仅清除本地Cookie并刷新页面
logout('https://your-domain.com/') // 重定向到IAM登出页面
```

### 4. 使用React Hooks获取用户信息

#### 4.1 使用原有的useUrlUserInfo Hook（已升级支持IAM）

```typescript
import useUrlUserInfo from '@/hooks/use-url-user-info'

const MyComponent = () => {
  const userInfo = useUrlUserInfo({
    onUserInfoChange: (userInfo) => {
      console.log('用户信息变化:', userInfo)
    }
  })

  return (
    <div>
      {userInfo.realName && <p>欢迎，{userInfo.realName}！</p>}
      {userInfo.phone && <p>手机号：{userInfo.phone}</p>}
    </div>
  )
}
```

#### 4.2 使用专门的IAM Hook

```typescript
import useIAMUserInfo from '@/hooks/use-iam-user-info'

const MyComponent = () => {
  const { isLoggedIn, userInfo, realName, logout } = useIAMUserInfo({
    onUserInfoChange: (userInfo) => {
      console.log('IAM用户信息变化:', userInfo)
    },
    onLoginStatusChange: (isLoggedIn) => {
      console.log('登录状态变化:', isLoggedIn)
    }
  })

  if (!isLoggedIn) {
    return <div>请先登录</div>
  }

  return (
    <div>
      <p>欢迎，{realName}！</p>
      <p>员工编码：{userInfo?.staffCode}</p>
      <button onClick={() => logout()}>登出</button>
    </div>
  )
}
```

#### 4.3 使用统一用户信息Hook

```typescript
import useUnifiedUserInfo from '@/hooks/use-unified-user-info'

const MyComponent = () => {
  const {
    userInfo,
    isLoading,
    hasUserInfo,
    isFromIAM,
    isFromURL,
    isFromCache
  } = useUnifiedUserInfo({
    onUserInfoChange: (userInfo) => {
      console.log('用户信息变化:', userInfo)
    },
    preferIAM: true // 优先使用IAM认证
  })

  if (isLoading) return <div>加载中...</div>
  if (!hasUserInfo) return <div>无用户信息</div>

  return (
    <div>
      <p>数据来源: {isFromIAM ? 'IAM认证' : isFromURL ? 'URL参数' : '本地缓存'}</p>
      <p>用户信息: {JSON.stringify(userInfo, null, 2)}</p>
    </div>
  )
}
```

## 安全特性

1. **Cookie安全**：设置 `SameSite=Lax` 属性
2. **参数加密**：使用base64编码传递参数
3. **Ticket验证**：验证ticket格式的有效性
4. **错误处理**：完善的错误处理和用户提示
5. **重试限制**：限制重试次数，防止无限重试

## 错误处理

- **网络错误**：自动重试，最多3次
- **参数错误**：友好的错误提示
- **认证失败**：显示具体错误信息
- **超时处理**：合理的超时设置

## 用户体验

- **加载状态**：显示认证进度
- **成功提示**：认证成功的反馈
- **错误提示**：清晰的错误信息
- **重试机制**：允许用户重试操作

## 兼容性

- ✅ 支持Next.js 13+ App Router
- ✅ 兼容TypeScript
- ✅ 适配项目现有的HTTP请求封装
- ✅ 使用项目现有的UI组件
- ✅ 支持多种后端响应格式

## 测试建议

1. **正常流程测试**：
   - 首次访问 → IAM认证 → 获取用户信息 → 跳转目标页面
   - 已有缓存 → 直接跳转目标页面

2. **异常流程测试**：
   - 无效ticket → 显示错误信息
   - 网络异常 → 重试机制
   - 参数解析失败 → 降级处理

3. **边界情况测试**：
   - 空参数处理
   - 特殊字符处理
   - 超长URL处理

## 注意事项

1. **域名配置**：生产环境需要配置正确的Cookie域名
2. **HTTPS要求**：生产环境建议使用HTTPS协议
3. **后端接口**：确保后端接口返回格式与前端适配
4. **路由冲突**：确保 `/ai_dify/loginIAM` 路由不与现有路由冲突

## 后续扩展

1. **路由拦截**：可以添加middleware实现全局路由守卫
2. **多域名支持**：支持多个子域名的Cookie共享
3. **登出功能**：集成IAM登出功能
4. **会话管理**：与现有的token机制集成

---

## 🎉 代码迁移完成

### 已替换的文件

以下文件已成功从`useUrlUserInfo`替换为`useUnifiedUserInfo`：

1. **`web/app/components/workflow/panel/debug-and-preview/chat-wrapper.tsx`**
   - ✅ 替换导入：`useUrlUserInfo` → `useUnifiedUserInfo`
   - ✅ 添加`preferIAM: true`配置
   - ✅ 保持原有的`onUserInfoChange`回调逻辑

2. **`web/app/components/base/chat/chat-with-history/chat-wrapper.tsx`**
   - ✅ 替换导入：`useUrlUserInfo` → `useUnifiedUserInfo`
   - ✅ 添加`preferIAM: true`配置
   - ✅ 保持原有的用户手机号设置逻辑

3. **`web/app/components/base/chat/chat-with-history/index.tsx`**
   - ✅ 替换导入：`useUrlUserInfo` → `useUnifiedUserInfo`
   - ✅ 添加`preferIAM: true`配置

### 向后兼容性保证

- **`useUrlUserInfo`** hook已更新为内部使用`useUnifiedUserInfo`，保持API完全兼容
- 现有代码无需修改，自动支持IAM认证
- 数据获取优先级：**IAM Cookie** → **URL参数** → **localStorage缓存**

### 新增的功能

1. **`useUnifiedUserInfo`** - 统一用户信息管理hook
2. **`useIAMUserInfo`** - 专门的IAM认证管理hook
3. **`UserInfoDisplay`** - 用户信息展示组件
4. **`/test-user-info`** - 测试页面，验证所有hooks工作情况

### 测试验证

访问以下页面进行测试：

```bash
# 测试页面 - 验证所有hooks工作情况
http://localhost:3000/test-user-info

# IAM认证页面
http://localhost:3000/ai_dify/loginIAM

# 带URL参数测试兼容性
http://localhost:3000/test-user-info?employeeNum=123&phone=13800138000&realName=测试用户
```

### 迁移优势

1. **无缝升级**：现有代码无需修改，自动支持IAM认证
2. **多数据源**：支持IAM、URL参数、本地缓存三种数据源
3. **优先级管理**：智能的数据获取优先级
4. **类型安全**：完整的TypeScript类型支持
5. **易于调试**：详细的控制台日志和数据来源标识

**🚀 现在您的应用已经完全支持IAM单点登录，同时保持对原有功能的完全兼容！**
